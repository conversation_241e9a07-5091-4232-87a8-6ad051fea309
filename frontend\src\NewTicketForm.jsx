import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function NewTicketForm({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    productType: "Camera",
    brand: "",
    sensorType: "",
    familyName: "",
    modelNumber: "",
    serialNumber: "",
    sdkSoftwareUsed: "",
    sdkVersion: "",
    programmingLanguage: "",
    cameraConfigurationTool: "",
    operatingSystemDetailed: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    poNumber: "",
    // Removed duplicates: productName (use productType), model (use modelNumber),
    // serialNo (use serialNumber), operatingSystem (use operatingSystemDetailed)
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    let updatedFormData = {
      ...formData,
      [name]: value
    };

    // Auto-filtering logic for Brand and Family Name
    if (name === 'brand' && value === 'DALSA') {
      updatedFormData.familyName = 'Genie Nano';
    } else if (name === 'familyName' && value === 'Genie Nano') {
      updatedFormData.brand = 'DALSA';
    }

    // Auto-filtering logic for SDK and SDK Version
    if (name === 'sdkSoftwareUsed' && value === 'Sapera LT') {
      updatedFormData.sdkVersion = 'v9.0';
    }

    setFormData(updatedFormData);
    if (error) setError("");
  };

  const validateForm = () => {
    const requiredFields = [
      'modelNumber', 'serialNumber', 'purchasedFrom', 'yearOfPurchase', 'operatingSystemDetailed', 'poNumber'
    ];

    for (let field of requiredFields) {
      if (!formData[field] || formData[field].trim() === "") {
        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
      }
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          product_type: formData.productType,
          brand: formData.brand,
          sensor_type: formData.sensorType,
          family_name: formData.familyName,
          model_number: formData.modelNumber,
          serial_number: formData.serialNumber,
          sdk_software_used: formData.sdkSoftwareUsed,
          sdk_version: formData.sdkVersion,
          programming_language: formData.programmingLanguage,
          camera_configuration_tool: formData.cameraConfigurationTool,
          operating_system_detailed: formData.operatingSystemDetailed,
          purchased_from: formData.purchasedFrom,
          year_of_purchase: formData.yearOfPurchase,
          po_number: formData.poNumber,
          // Removed duplicates: product_name, model, serial_no, operating_system
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to problem category selection
        navigate(`/problem-categories?ticket=${data.ticket_number}`);
      } else {
        setError(data.message || "Failed to create ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/actions");
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Create New Support Ticket
      </h1>
      
      <p style={{
        fontSize: "1.1rem",
        color: "#666",
        marginBottom: "30px",
        textAlign: "center"
      }}>
        Please provide your product details below. The system will automatically generate a problem description and title for your ticket.
      </p>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <form
        onSubmit={handleSubmit}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          maxHeight: "70vh",
          overflowY: "auto",
          padding: "10px"
        }}
      >



        {/* Product Type */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Product Type *
          </label>
          <select
            name="productType"
            value={formData.productType}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="Camera">Camera</option>
            <option value="Frame Grabber">Frame Grabber</option>
            <option value="Accessories">Accessories</option>
            <option value="Software">Software</option>
          </select>
        </div>

        {/* Brand */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Brand
          </label>
          <select
            name="brand"
            value={formData.brand}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Brand</option>
            <option value="DALSA">DALSA</option>
            <option value="FLIR">FLIR</option>
            <option value="Basler">Basler</option>
            <option value="Allied Vision">Allied Vision</option>
            <option value="Cognex">Cognex</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Sensor Type */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Sensor Type
          </label>
          <select
            name="sensorType"
            value={formData.sensorType}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Sensor Type</option>
            <option value="Area Scan">Area Scan</option>
            <option value="Line Scan">Line Scan</option>
          </select>
        </div>

        {/* Family Name */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Family Name
          </label>
          <select
            name="familyName"
            value={formData.familyName}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Family Name</option>
            {formData.brand === 'DALSA' ? (
              <option value="Genie Nano">Genie Nano</option>
            ) : (
              <>
                <option value="Genie Nano">Genie Nano</option>
                <option value="Linea">Linea</option>
                <option value="Ace">Ace</option>
                <option value="Dart">Dart</option>
                <option value="Other">Other</option>
              </>
            )}
          </select>
        </div>

        {/* Model Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Model Number
          </label>
          <input
            type="text"
            name="modelNumber"
            value={formData.modelNumber}
            onChange={handleInputChange}
            placeholder="e.g., C1280M-25G"
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Serial Number (New) */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Serial Number (Camera)
          </label>
          <input
            type="text"
            name="serialNumber"
            value={formData.serialNumber}
            onChange={handleInputChange}
            placeholder="e.g., 12345678"
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* SDK / Software Used */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            SDK / Software Used
          </label>
          <select
            name="sdkSoftwareUsed"
            value={formData.sdkSoftwareUsed}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select SDK/Software</option>
            <option value="Sapera LT">Sapera LT</option>
            <option value="Spinnaker">Spinnaker</option>
            <option value="Pylon">Pylon</option>
            <option value="VisionPoint">VisionPoint</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* SDK Version */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            SDK Version
          </label>
          <select
            name="sdkVersion"
            value={formData.sdkVersion}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select SDK Version</option>
            {formData.sdkSoftwareUsed === 'Sapera LT' ? (
              <option value="v9.0">v9.0</option>
            ) : (
              <>
                <option value="v6.10">v6.10</option>
                <option value="v7.0">v7.0</option>
                <option value="v8.0">v8.0</option>
                <option value="v9.0">v9.0</option>
                <option value="Other">Other</option>
              </>
            )}
          </select>
        </div>

        {/* Programming Language */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Programming Language
          </label>
          <select
            name="programmingLanguage"
            value={formData.programmingLanguage}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Programming Language</option>
            <option value="C#">C#</option>
            <option value="C++">C++</option>
            <option value="Python">Python</option>
            <option value="Java">Java</option>
            <option value="LabVIEW">LabVIEW</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Camera Configuration Tool */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Camera Configuration Tool
          </label>
          <select
            name="cameraConfigurationTool"
            value={formData.cameraConfigurationTool}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Configuration Tool</option>
            <option value="IP Config Tool">IP Config Tool</option>
            <option value="eBUS Player">eBUS Player</option>
            <option value="Pylon Viewer">Pylon Viewer</option>
            <option value="CamExpert">CamExpert</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Operating System (Detailed) */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Operating System
          </label>
          <select
            name="operatingSystemDetailed"
            value={formData.operatingSystemDetailed}
            onChange={handleInputChange}
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Operating System</option>
            <option value="Windows 10">Windows 10</option>
            <option value="Windows 11">Windows 11</option>
            <option value="Linux Ubuntu 20.04">Linux Ubuntu 20.04</option>
            <option value="Linux Ubuntu 22.04">Linux Ubuntu 22.04</option>
            <option value="CentOS">CentOS</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Product Name removed - using Product Type instead */}

        {/* Model removed - using Model Number instead */}

        {/* Serial Number removed - using Serial Number (Camera) instead */}

        {/* Purchased From */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Purchased From *
          </label>
          <input
            type="text"
            name="purchasedFrom"
            value={formData.purchasedFrom}
            onChange={handleInputChange}
            placeholder="e.g., Teledyne DALSA, Authorized Dealer"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Year of Purchase */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Year of Purchase *
          </label>
          <input
            type="text"
            name="yearOfPurchase"
            value={formData.yearOfPurchase}
            onChange={handleInputChange}
            placeholder="e.g., 2023"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Operating System removed - using Operating System (Detailed) instead */}

        {/* PO Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            PO Number *
          </label>
          <input
            type="text"
            name="poNumber"
            value={formData.poNumber}
            onChange={handleInputChange}
            placeholder="Purchase Order Number"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>



        {/* Buttons */}
        <div style={{ display: "flex", gap: "15px", marginTop: "20px" }}>
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            style={{
              flex: 1,
              padding: "15px",
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer"
            }}
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={loading}
            style={{
              flex: 2,
              padding: "15px",
              backgroundColor: loading ? "#ccc" : "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer"
            }}
          >
            {loading ? "Creating Ticket..." : "Create Ticket & Start Chat"}
          </button>
        </div>
      </form>
    </div>
  );
}
