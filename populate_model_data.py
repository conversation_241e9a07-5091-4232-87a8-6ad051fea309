#!/usr/bin/env python3
"""
<PERSON>ript to populate model data for PDF files based on their filenames.
This is for testing the model-specific search functionality.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_chatbot_backend.settings')
django.setup()

from chatbot.models import PdfFile

def populate_model_data():
    """Populate model data for PDF files based on their filenames."""
    
    print("🔧 Populating Model Data for PDF Files")
    print("=" * 50)
    
    # Define model mappings based on filename patterns
    model_mappings = {
        # Genie Nano series
        "genie": ["Genie Nano", "Genie Nano 5G", "Genie Nano 10G"],
        "nano": ["Genie Nano", "Genie Nano 5G", "Genie Nano 10G"],
        
        # Falcon series
        "falcon": ["Falcon4-CLHS", "Falcon4"],
        
        # Xtium series
        "xtium": ["Xtium2-CL", "Xtium2-XGV", "Xtium-CLHS"],
        
        # Specific models
        "c1280": ["C1280M-25G"],
        "sherlock": ["Sherlock8"],
        "streampix": ["StreamPix"],
        "sapera": ["Sapera LT", "Sapera++"],
        "spinnaker": ["Spinnaker SDK"],
        
        # General categories
        "terahertz": ["Terahertz Camera"],
        "troubleshoot": ["General Troubleshooting"],
        "manual": ["User Manual"],
        "datasheet": ["Product Datasheet"],
    }
    
    pdf_files = PdfFile.objects.all()
    updated_count = 0
    
    for pdf_file in pdf_files:
        filename_lower = pdf_file.file_name.lower()
        models_for_file = []
        
        # Check each pattern and add matching models
        for pattern, models in model_mappings.items():
            if pattern in filename_lower:
                models_for_file.extend(models)
        
        # Remove duplicates while preserving order
        models_for_file = list(dict.fromkeys(models_for_file))
        
        # Update the model field if we found any matches
        if models_for_file:
            pdf_file.model = models_for_file
            pdf_file.save(update_fields=['model'])
            updated_count += 1
            print(f"✅ Updated {pdf_file.file_name}")
            print(f"   Models: {models_for_file}")
        else:
            print(f"⚠️  No models found for {pdf_file.file_name}")
    
    print(f"\n📊 Summary: Updated {updated_count} out of {pdf_files.count()} PDF files")

def show_current_model_data():
    """Display current model data for all PDF files."""
    
    print("\n📋 Current Model Data:")
    print("-" * 50)
    
    pdf_files = PdfFile.objects.all()
    
    for pdf_file in pdf_files:
        print(f"📄 {pdf_file.file_name}")
        if pdf_file.model:
            print(f"   Models: {pdf_file.model}")
        else:
            print(f"   Models: (none)")

if __name__ == "__main__":
    try:
        populate_model_data()
        show_current_model_data()
        print("\n✅ Model data population completed!")
    except Exception as e:
        print(f"\n❌ Failed with error: {e}")
        import traceback
        traceback.print_exc()
