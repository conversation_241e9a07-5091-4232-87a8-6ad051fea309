#!/usr/bin/env python3
"""
Additional test script to verify model-specific search with more models.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_chatbot_backend.settings')
django.setup()

from chatbot.views import get_model_specific_pdf_files

def test_additional_models():
    """Test additional model searches."""
    
    print("🧪 Testing Additional Model Searches")
    print("=" * 50)
    
    test_models = [
        "Sapera LT",
        "Sapera++", 
        "Spinnaker SDK",
        "Terahertz Camera",
        "User Manual",
        "5G",
        "10G",
        "nano",
        "genie",
        "sapera"
    ]
    
    for model in test_models:
        print(f"\n🔍 Searching for model: '{model}'")
        result = get_model_specific_pdf_files(model)
        print(f"  Found {len(result)} matching files:")
        for file_name in result[:3]:  # Show first 3 files
            print(f"    - {file_name}")
        if len(result) > 3:
            print(f"    ... and {len(result) - 3} more files")

if __name__ == "__main__":
    try:
        test_additional_models()
        print("\n✅ Additional tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
