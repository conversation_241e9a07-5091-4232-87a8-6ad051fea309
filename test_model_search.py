#!/usr/bin/env python3
"""
Test script to verify the model-specific PDF search functionality.
This script tests the get_model_specific_pdf_files function.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_chatbot_backend.settings')
django.setup()

from chatbot.views import get_model_specific_pdf_files
from chatbot.models import PdfFile

def test_model_search():
    """Test the model-specific PDF search functionality."""
    
    print("🧪 Testing Model-Specific PDF Search Functionality")
    print("=" * 60)
    
    # Test 1: Check if function handles empty/None model numbers
    print("\n📋 Test 1: Empty/None model numbers")
    result = get_model_specific_pdf_files("")
    print(f"Empty string result: {result}")
    
    result = get_model_specific_pdf_files(None)
    print(f"None result: {result}")
    
    # Test 2: Check current PDF files in database
    print("\n📋 Test 2: Current PDF files in database")
    pdf_files = PdfFile.objects.all()
    print(f"Total PDF files in database: {pdf_files.count()}")
    
    for pdf_file in pdf_files[:5]:  # Show first 5 files
        print(f"  - {pdf_file.file_name}")
        print(f"    Model data: {pdf_file.model}")
        print(f"    Model type: {type(pdf_file.model)}")
    
    if pdf_files.count() > 5:
        print(f"  ... and {pdf_files.count() - 5} more files")
    
    # Test 3: Test with sample model numbers
    print("\n📋 Test 3: Testing with sample model numbers")
    
    test_models = [
        "C1280M-25G",
        "Genie Nano",
        "Falcon4",
        "Xtium",
        "nonexistent-model"
    ]
    
    for model in test_models:
        print(f"\n🔍 Searching for model: '{model}'")
        result = get_model_specific_pdf_files(model)
        print(f"  Found {len(result)} matching files:")
        for file_name in result:
            print(f"    - {file_name}")
    
    # Test 4: Test case sensitivity
    print("\n📋 Test 4: Testing case sensitivity")
    test_cases = [
        ("genie nano", "lowercase"),
        ("GENIE NANO", "uppercase"),
        ("Genie Nano", "mixed case")
    ]
    
    for model, description in test_cases:
        print(f"\n🔍 Testing {description}: '{model}'")
        result = get_model_specific_pdf_files(model)
        print(f"  Found {len(result)} matching files")

if __name__ == "__main__":
    try:
        test_model_search()
        print("\n✅ Test completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
